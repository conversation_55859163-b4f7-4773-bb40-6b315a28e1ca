# Branching Strategy

## Overview

This document outlines our Git branching strategy based on GitFlow.

## Branch Types

### Main Branches
- **`main`**: Production-ready code
- **`develop`**: Integration branch for features

### Supporting Branches
- **`feature/*`**: New features (branch from `develop`)
- **`release/*`**: Release preparation (branch from `develop`)
- **`hotfix/*`**: Emergency fixes (branch from `main`)

## Workflow

1. **Feature Development**
   ```bash
   git checkout develop
   git checkout -b feature/my-feature
   # Make changes
   git push origin feature/my-feature
   # Create PR to develop
   ```

2. **Release Preparation**
   ```bash
   git checkout develop
   git checkout -b release/v1.0.0
   # Final testing and fixes
   git push origin release/v1.0.0
   # Create PR to main AND develop
   ```

3. **Hotfix**
   ```bash
   git checkout main
   git checkout -b hotfix/critical-fix
   # Fix the issue
   git push origin hotfix/critical-fix
   # Create PR to main AND develop
   ```

## Branch Protection Rules

- `main`: Requires PR approval and passing CI
- `develop`: Requires PR approval and passing CI
- `release/*`: Requires PR approval and passing CI